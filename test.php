<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\Request;

class TriKeyController extends Controller
{
    const SUPER_HOT_FREQ = 8;
    const HOT_FREQ = 7;
    const BASIC_FREQ = 3;
    const RECENT_PERIODS = 5;
    const RECENT_COUNT_AB = 4;

    // Bộ quy tắc loại trừ đặc biệt theo [A, B, C, optional countSingle threshold]
    private array $specialExcludes = [
        [10, 6, 4], [9, 7, 6],
    ];

    public function cacheOf($keyOf)
    {
        return 'main_16_' . $keyOf;
    }

    public function getData($day = 0, $page = 0, $sort = 'ASC')
    {
        $day = (int)$day;
        $from = now()->copy()->addDays(-$day)->endOfDay()->format('Y/m/d');
        $to = now()->copy()->addDays(1 - $day)->endOfDay()->format('Y/m/d');

        $url = 'https://api-knlt.gamingon.net/api/v1/rounds/1?limit=30&status=ENDED&sort=' . $sort . '&page=' . $page . '&from_date=' . $from . '&to_date=' . $to;
        $response = Http::withHeaders(['Content-Type' => 'application/json'])->get($url);

        return json_decode($response, true)['content'];
    }

    public function getResultByDay($day = 1, $to = 0, $sort = 'ASC')
    {
        $nameCache = 'getResultByDay123' . $day . '_' . $to;
        if (!$response = Cache::get($this->cacheOf($nameCache))) {
            $c = [];
            for ($b = 3; $b >= 0; $b--) {
                $response = $this->getData($day, $b, 'DESC');
                $c = array_merge($response, $c);
            }
            Cache::put($this->cacheOf($nameCache), $c, 60);
            $response = $c;
        }

        return $sort === 'ASC' ? array_reverse($response) : $response;
    }

    public function show(Request $req)
    {
        ini_set('memory_limit', -1);
        set_time_limit(1500);
        $dayTest = $req->get('day', 1);
        $slice = (int)$req->get('slice', 119);
        $data = $this->getResultByDay($dayTest);

        $historyOfDay = array_map(fn($itemH) => array_map('intval', explode('|', $itemH['resultRaw'])), $data);

        $history = $historyOfDay;
        $numbers = 50;
        $draws70 = array_slice($history, 0, $numbers);
        $draws49 = array_slice($history, 13, $slice);


        $flat = array_merge(...$draws70);
        $freq70 = array_count_values($flat);
        asort($freq70);

        $coldNums = array_slice(array_keys($freq70), 0, 80);
        $numComb = (int)$req->get('comb', 3);
        $comb = $this->combinations($coldNums, $numComb);

        $agg = array_fill(0, 60, 0);
        $excludedCombos = [];
        $detail10 = [];

        foreach ($comb as $triple) {
            $streak = 0;
            $maxStreak = 0;

            foreach ($draws49 as $i => $draw) {
                $hit = count(array_intersect($triple, $draw));

                if ($hit > 0) {
                    $streak++;
                    $maxStreak = max($maxStreak, $streak);

                } else {
                    if ($streak > 0 && !in_array($triple, $excludedCombos)) {
                        $agg[$streak]++;
                        $detail10[] = [
                            'board' => $triple,
                            'total' => $streak
                        ];
                    }
                    $streak = 0;
                }
            }

            if ($streak > 0 && !in_array($triple, $excludedCombos)) {
                $agg[$streak]++;
                $detail10[] = [
                    'board' => $triple,
                    'total' => $streak
                ];
            }
        }

        return view('tri_key_stats', [
            'date' => $dayTest,
            'agg' => $agg,
            'excludedCombos' => $excludedCombos,
            'detail10' => $detail10,
        ]);
    }

    private function combinations(array $arr, int $k): array
    {
        if ($k === 0) return [[]];
        if (count($arr) < $k) return [];
        $head = $arr[0];
        $rest = array_slice($arr, 1);
        return array_merge(
            array_map(fn($c) => array_merge([$head], $c), $this->combinations($rest, $k - 1)),
            $this->combinations($rest, $k)
        );
    }
}
